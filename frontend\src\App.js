import { useState, useEffect } from "react";
import { Routes, Route, Navigate } from "react-router-dom";

import Topbar from "./pages/global/Topbar";
import Sidebar from "./pages/global/Sidebar";
import Dashboard from "./pages/DashboardPage";
import Login from "./pages/LoginPage";
import InspectionFormPage from "./pages/InspectionFormPage";
import Documents from "./pages/Documents";

function App() {
  const [isSidebar, setIsSidebar] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false); // Add authentication state

  // Check authentication state on app load
  useEffect(() => {
    const currentUser = localStorage.getItem('currentUser');
    const currentUsername = localStorage.getItem('currentUsername');

    if (currentUser && currentUsername) {
      setIsAuthenticated(true);
    }
  }, []);

  return (
    <div className="app">
      {isAuthenticated ? (
        <>
          <Sidebar isSidebar={isSidebar} />
          <main className="content">
            <Topbar setIsSidebar={setIsSidebar} />
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/InspectionFormPage" element={<InspectionFormPage/>} />
              <Route path="/documents" element={<Documents />} />
              {/* Add other routes here as needed */}
            </Routes>
          </main>
        </>
      ) : (
        <Routes>
          <Route path="/login" element={<Login setIsAuthenticated={setIsAuthenticated} />} />
          <Route path="*" element={<Navigate to="/login" />} />
        </Routes>
      )}
    </div>
  );
}

export default App;