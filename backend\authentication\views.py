from django.shortcuts import render
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie, csrf_exempt
from django.utils.decorators import method_decorator
from .serializers import RegisterSerializer, UserSerializer
from .models import PasswordResetToken
from django.core.mail import send_mail
from django.conf import settings
import uuid

# Create your views here.
#this worked

class CSRFTokenView(APIView):
    permission_classes = (permissions.AllowAny,)

    def get(self, request):
        token = get_token(request)
        return Response({'csrfToken': token})

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    permission_classes = (permissions.AllowAny,)
    serializer_class = RegisterSerializer

@method_decorator(csrf_exempt, name='dispatch')
class LoginView(APIView):
    permission_classes = (permissions.AllowAny,)

    def get(self, request):
        # Handle GET requests (for debugging or CSRF token)
        return Response({
            'message': 'Login endpoint is working. Use POST to login.',
            'methods': ['POST']
        }, status=status.HTTP_200_OK)

    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')

        if username and password:
            user = authenticate(request, username=username, password=password)
            if user:
                login(request, user)
                # Store current user info in session for profile access
                request.session['current_user_id'] = user.id
                request.session['current_username'] = user.username
                return Response({
                    'message': 'Login successful',
                    'user': UserSerializer(user).data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Invalid credentials'
                }, status=status.HTTP_401_UNAUTHORIZED)
        else:
            return Response({
                'error': 'Username and password required'
            }, status=status.HTTP_400_BAD_REQUEST)

class LogoutView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        logout(request)
        return Response({
            'message': 'Logout successful'
        }, status=status.HTTP_200_OK)

@method_decorator(csrf_exempt, name='dispatch')
class UserProfileView(generics.RetrieveUpdateAPIView):
    permission_classes = (permissions.AllowAny,)  # Temporarily remove auth requirement
    serializer_class = UserSerializer

    def get_object(self):
        # Return user based on username parameter or session info
        from django.contrib.auth.models import User

        # Check if username is provided as query parameter
        username = self.request.query_params.get('username')
        if username:
            try:
                return User.objects.get(username=username)
            except User.DoesNotExist:
                pass

        # Check if we have current user info in session
        current_user_id = self.request.session.get('current_user_id')
        current_username = self.request.session.get('current_username')

        if current_user_id:
            try:
                return User.objects.get(id=current_user_id)
            except User.DoesNotExist:
                pass

        if current_username:
            try:
                return User.objects.get(username=current_username)
            except User.DoesNotExist:
                pass

        # Fallback to ilhem98 if no session info
        try:
            return User.objects.get(username='ilhem98')
        except User.DoesNotExist:
            return User.objects.first()


class ForgotPasswordView(APIView):
    permission_classes = (permissions.AllowAny,)

    def post(self, request):
        email = request.data.get('email')

        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # Create password reset token
            reset_token = PasswordResetToken.objects.create(user=user)

            # For now, just return the token (in production, send email)
            # TODO: Implement email sending
            reset_url = f"https://e-inspection-form-4.onrender.com/reset-password?token={reset_token.token}"

            return Response({
                'message': 'Password reset instructions have been sent to your email.',
                'reset_url': reset_url,  # Remove this in production
                'token': str(reset_token.token)  # Remove this in production
            }, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            # Don't reveal if email exists or not for security
            return Response({
                'message': 'If an account with this email exists, password reset instructions have been sent.'
            }, status=status.HTTP_200_OK)


class ResetPasswordView(APIView):
    permission_classes = (permissions.AllowAny,)

    def post(self, request):
        token = request.data.get('token')
        new_password = request.data.get('new_password')

        if not token or not new_password:
            return Response({'error': 'Token and new password are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            reset_token = PasswordResetToken.objects.get(token=token)

            if not reset_token.is_valid():
                return Response({'error': 'Token is invalid or expired'}, status=status.HTTP_400_BAD_REQUEST)

            # Reset the password
            user = reset_token.user
            user.set_password(new_password)
            user.save()

            # Mark token as used
            reset_token.is_used = True
            reset_token.save()

            return Response({'message': 'Password has been reset successfully'}, status=status.HTTP_200_OK)

        except PasswordResetToken.DoesNotExist:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)
