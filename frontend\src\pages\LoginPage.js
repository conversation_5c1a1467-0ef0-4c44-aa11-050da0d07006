import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { login, register } from '../services/api';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

function LoginPage({ setIsAuthenticated }) {
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    password2: '',
    email: '',
    first_name: '',
    last_name: ''
  });
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showPassword2, setShowPassword2] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    try {
      if (isSignUp) {
        // Registration
        if (formData.password !== formData.password2) {
          setError('Passwords do not match');
          return;
        }
        await register(formData);
        // After successful registration, switch to login
        setIsSignUp(false);
        setFormData({ ...formData, password: '', password2: '' });
      } else {
        // Login
        const loginResponse = await login(formData.username, formData.password);

        // Store current user data in localStorage
        if (loginResponse && loginResponse.user) {
          localStorage.setItem('currentUser', JSON.stringify(loginResponse.user));
          localStorage.setItem('currentUsername', loginResponse.user.username);
        }

        setIsAuthenticated(true);
        navigate('/');
      }
    } catch (error) {
      // Handle registration errors
      if (isSignUp && error.password) {
        setError(`Password requirements: ${error.password.join(' ')}`);
      } else if (isSignUp && error.username) {
        setError(`Username error: ${error.username.join(' ')}`);
      } else if (isSignUp && error.email) {
        setError(`Email error: ${error.email.join(' ')}`);
      } else if (error.error === 'Invalid credentials') {
        setError('Invalid username or password. Please try again.');
      } else if (error.message && error.message.includes('Invalid credentials')) {
        setError('Invalid username or password. Please try again.');
      } else if (error.message && error.message.includes('401')) {
        setError('Invalid username or password. Please try again.');
      } else if (error.message) {
        setError(error.message);
      } else {
        setError(isSignUp ? 'Registration failed. Please check your information and try again.' : 'Login failed. Please check your credentials and try again.');
      }
    }
  };

  const toggleForm = () => {
    setIsSignUp(!isSignUp);
    setError('');
    setFormData({
      username: '',
      password: '',
      password2: '',
      email: '',
      first_name: '',
      last_name: ''
    });
  };

  return (
    <div style={styles.outerContainer}>
      <div style={styles.container}>
        <h2 style={styles.heading}>{isSignUp ? 'Sign Up' : 'Sign In'}</h2>
        {error && <p style={styles.error}>{error}</p>}
        <form onSubmit={handleSubmit} style={styles.form}>
          <input
            type="text"
            name="username"
            placeholder="Username"
            value={formData.username}
            onChange={handleChange}
            style={styles.input}
            required
          />
          {isSignUp && (
            <>
              <input
                type="email"
                name="email"
                placeholder="Email"
                value={formData.email}
                onChange={handleChange}
                style={styles.input}
                required
              />
              <input
                type="text"
                name="first_name"
                placeholder="First Name"
                value={formData.first_name}
                onChange={handleChange}
                style={styles.input}
                required
              />
              <input
                type="text"
                name="last_name"
                placeholder="Last Name"
                value={formData.last_name}
                onChange={handleChange}
                style={styles.input}
                required
              />
            </>
          )}
          <div style={styles.passwordContainer}>
            <input
              type={showPassword ? "text" : "password"}
              name="password"
              placeholder="Password"
              value={formData.password}
              onChange={handleChange}
              style={styles.passwordInput}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={styles.passwordToggle}
            >
              {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
            </button>
          </div>
          {isSignUp && (
            <>
              <div style={styles.passwordHelp}>
                Password must be at least 8 characters and not too common
              </div>
              <div style={styles.passwordContainer}>
                <input
                  type={showPassword2 ? "text" : "password"}
                  name="password2"
                  placeholder="Confirm Password"
                  value={formData.password2}
                  onChange={handleChange}
                  style={styles.passwordInput}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword2(!showPassword2)}
                  style={styles.passwordToggle}
                >
                  {showPassword2 ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </button>
              </div>
            </>
          )}
          <button type="submit" style={styles.button}>
            {isSignUp ? 'Sign Up' : 'Login'}
          </button>
        </form>

        {!isSignUp && (
          <p style={styles.forgotPassword} onClick={() => alert('Forgot password functionality will be implemented soon.')}>
            Forgot Password?
          </p>
        )}

        <p onClick={toggleForm} style={styles.toggleText}>
          {isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up"}
        </p>
      </div>
    </div>
  );
}

const styles = {
  outerContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    width: '100vw',
    backgroundColor: '#004381',
    margin: 0,
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '20px',
    backgroundColor: '#fff',
    borderRadius: '8px',
    boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '400px',
  },
  form: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  heading: {
    color: '#004381',
    marginBottom: '20px',
  },
  input: {
    padding: '10px',
    margin: '10px 0',
    width: '100%',
    boxSizing: 'border-box',
    borderRadius: '4px',
    border: '1px solid #ddd',
  },
  button: {
    padding: '10px 20px',
    backgroundColor: '#004381',
    color: '#fff',
    border: 'none',
    cursor: 'pointer',
    marginTop: '10px',
    width: '100%',
    borderRadius: '4px',
  },
  toggleText: {
    marginTop: '10px',
    color: '#004381',
    cursor: 'pointer',
  },
  forgotPassword: {
    marginTop: '10px',
    marginBottom: '5px',
    color: '#666',
    cursor: 'pointer',
    fontSize: '14px',
    textAlign: 'center',
    textDecoration: 'underline',
  },
  error: {
    color: '#d32f2f',
    backgroundColor: '#ffebee',
    border: '1px solid #ffcdd2',
    borderRadius: '4px',
    padding: '10px',
    marginBottom: '15px',
    fontSize: '14px',
    textAlign: 'center',
    fontWeight: '500',
  },
  passwordHelp: {
    fontSize: '12px',
    color: '#666',
    marginTop: '-8px',
    marginBottom: '10px',
    textAlign: 'left',
  },
  passwordContainer: {
    position: 'relative',
    width: '100%',
    margin: '10px 0',
  },
  passwordInput: {
    padding: '10px 40px 10px 10px',
    width: '100%',
    boxSizing: 'border-box',
    borderRadius: '4px',
    border: '1px solid #ddd',
  },
  passwordToggle: {
    position: 'absolute',
    right: '10px',
    top: '50%',
    transform: 'translateY(-50%)',
    background: 'none',
    border: 'none',
    cursor: 'pointer',
    fontSize: '16px',
    padding: '0',
    color: '#666',
  },
};

export default LoginPage;