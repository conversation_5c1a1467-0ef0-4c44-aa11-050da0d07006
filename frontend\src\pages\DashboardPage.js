import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  CircularProgress,
  Alert,
  Button,
  IconButton
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer
} from 'recharts';
import { getInspections } from '../services/api';

const DashboardPage = () => {
  const [inspections, setInspections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Color schemes for charts
  const COLORS = {
    adequate: '#4CAF50',    // Green
    inadequate: '#F44336',  // Red
    certified: '#2196F3',   // Blue
    nonCertified: '#FF9800', // Orange
    readable: '#4CAF50',    // Green
    unreadable: '#F44336'   // Red
  };

  const PIE_COLORS = ['#4CAF50', '#F44336', '#2196F3', '#FF9800', '#9C27B0', '#607D8B', '#795548', '#E91E63'];

  useEffect(() => {
    fetchInspections();
  }, []);

  // Test function to directly call the backend API
  const testBackendConnection = async () => {
    try {
      console.log('Dashboard: Testing direct backend connection...');
      const response = await fetch('https://e-inspection-form-3.onrender.com/api/inspections/inspections/');
      console.log('Dashboard: Direct API response status:', response.status);
      const data = await response.json();
      console.log('Dashboard: Direct API response data:', data);
      return data;
    } catch (error) {
      console.error('Dashboard: Direct API test failed:', error);
      return null;
    }
  };

  const fetchInspections = async () => {
    try {
      setLoading(true);
      const currentUsername = localStorage.getItem('currentUsername');
      console.log('Dashboard: Current username from localStorage:', currentUsername);

      // Use the EXACT same approach as Documents.js - fetch ALL inspections
      console.log('Dashboard: Fetching ALL inspections (same as Documents.js)...');
      const inspectionsData = await getInspections(null);
      console.log('Dashboard: Fetched inspections data:', inspectionsData);
      console.log('Dashboard: Number of inspections:', inspectionsData?.length || 0);

      if (inspectionsData && Array.isArray(inspectionsData)) {
        if (inspectionsData.length > 0) {
          console.log('Dashboard: Sample inspection data structure:', inspectionsData[0]);
          console.log('Dashboard: Available fields in first inspection:', Object.keys(inspectionsData[0]));

          // Log some key fields to verify data structure
          const sample = inspectionsData[0];
          console.log('Dashboard: Key ATEX fields in sample:', {
            atex_oui: sample.atex_oui,
            atex_non: sample.atex_non,
            marquage_atex_g: sample.marquage_atex_g,
            marquage_atex_d: sample.marquage_atex_d,
            marquage_us: sample.marquage_us,
            acces_illisible: sample.acces_illisible,
            acces_pas_plaque: sample.acces_pas_plaque,
            equipement: sample.equipement,
            unite: sample.unite,
            projet: sample.projet
          });
        }
        setInspections(inspectionsData);
        setError('');
        console.log('Dashboard: Successfully loaded', inspectionsData.length, 'inspections');
      } else {
        console.warn('Dashboard: Invalid data format received:', typeof inspectionsData);
        setInspections([]);
        setError('Format de données invalide');
      }
    } catch (err) {
      const errorMessage = err?.message || err?.error || 'Erreur lors du chargement des données';
      setError(errorMessage);
      console.error('Dashboard: Error fetching inspections:', err);
      console.error('Dashboard: Error details:', {
        message: err?.message,
        response: err?.response?.data,
        status: err?.response?.status
      });
      setInspections([]);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check if equipment is ATEX certified
  const isATEXCertified = (inspection) => {
    // Check if any ATEX marking is present and equipment has a plaque
    const hasATEXMarking = !!(inspection.marquage_atex_g || inspection.marquage_atex_d || inspection.marquage_us);
    const hasPlaque = !inspection.acces_pas_plaque;
    return hasATEXMarking && hasPlaque;
  };

  // Helper function to check ATEX adequacy
  const isATEXAdequate = (inspection) => {
    // Equipment is adequate if atex_oui is true and atex_non is false
    return inspection.atex_oui === true && inspection.atex_non !== true;
  };

  // Helper function to check marking readability
  const isMarkingReadable = (inspection) => {
    // Marking is readable if acces_illisible is false
    return !inspection.acces_illisible;
  };

  // Helper function to get equipment type (handle custom equipment)
  const getEquipmentType = (inspection) => {
    return inspection.equipement_custom || inspection.equipement || 'Type non spécifié';
  };

  // Helper function to get unit name (handle custom unit)
  const getUnitName = (inspection) => {
    return inspection.unite_custom || inspection.unite || inspection.localisation || 'Unité non spécifiée';
  };

  // 1. Data for equipment adequacy by unit (Bar Chart)
  const getAdequacyByUnit = () => {
    const unitData = {};

    inspections.forEach(inspection => {
      const unit = getUnitName(inspection);
      if (!unitData[unit]) {
        unitData[unit] = { unite: unit, nombre_adequats: 0, nombre_inadequats: 0 };
      }

      if (isATEXAdequate(inspection)) {
        unitData[unit].nombre_adequats++;
      } else {
        unitData[unit].nombre_inadequats++;
      }
    });

    const result = Object.values(unitData);
    console.log('Adequacy by unit data:', result);
    return result;
  };

  // 2. Data for global ATEX adequacy (Pie Chart)
  const getGlobalAdequacy = () => {
    let adequats = 0;
    let inadequats = 0;

    inspections.forEach(inspection => {
      if (isATEXAdequate(inspection)) {
        adequats++;
      } else {
        inadequats++;
      }
    });

    const total = adequats + inadequats;
    return [
      {
        name: 'Adéquats',
        value: adequats,
        percentage: total > 0 ? ((adequats / total) * 100).toFixed(1) : 0
      },
      {
        name: 'Inadéquats',
        value: inadequats,
        percentage: total > 0 ? ((inadequats / total) * 100).toFixed(1) : 0
      }
    ];
  };

  // 3. Data for inadequate equipment by type (Bar Chart)
  const getInadequateByType = () => {
    const typeData = {};

    inspections.forEach(inspection => {
      const type = getEquipmentType(inspection);
      if (!typeData[type]) {
        typeData[type] = { type_equipement: type, nombre_adequats: 0, nombre_inadequats: 0 };
      }

      if (isATEXAdequate(inspection)) {
        typeData[type].nombre_adequats++;
      } else {
        typeData[type].nombre_inadequats++;
      }
    });

    const result = Object.values(typeData);
    console.log('Inadequate by type data:', result);
    return result;
  };

  // 4. Data for ATEX certification (Pie Chart)
  const getATEXCertification = () => {
    let certified = 0;
    let nonCertified = 0;

    inspections.forEach(inspection => {
      if (isATEXCertified(inspection)) {
        certified++;
      } else {
        nonCertified++;
      }
    });

    const total = certified + nonCertified;
    return [
      {
        name: 'Certifiés ATEX',
        value: certified,
        percentage: total > 0 ? ((certified / total) * 100).toFixed(1) : 0
      },
      {
        name: 'Non certifiés ATEX',
        value: nonCertified,
        percentage: total > 0 ? ((nonCertified / total) * 100).toFixed(1) : 0
      }
    ];
  };

  // 5. Data for non-ATEX equipment by type (Pie Chart)
  const getNonATEXByType = () => {
    const typeData = {};

    inspections.forEach(inspection => {
      if (!isATEXCertified(inspection)) {
        const type = getEquipmentType(inspection);
        typeData[type] = (typeData[type] || 0) + 1;
      }
    });

    const total = Object.values(typeData).reduce((sum, count) => sum + count, 0);

    const result = Object.entries(typeData).map(([type, count]) => ({
      type,
      count,
      percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    }));

    console.log('Non-ATEX by type data:', result);
    return result;
  };

  // 6. Data for marking readability by unit (Bar Chart)
  const getReadabilityByUnit = () => {
    const unitData = {};

    inspections.forEach(inspection => {
      if (isATEXCertified(inspection)) {
        const unit = getUnitName(inspection);
        if (!unitData[unit]) {
          unitData[unit] = { unite: unit, lisibles: 0, illisibles: 0 };
        }

        if (isMarkingReadable(inspection)) {
          unitData[unit].lisibles++;
        } else {
          unitData[unit].illisibles++;
        }
      }
    });

    const result = Object.values(unitData);
    console.log('Readability by unit data:', result);
    return result;
  };


  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, ...rest }) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 1.2;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  const name = rest.name || rest.type;
  const percentage = rest.percentage || (percent * 100).toFixed(1);
  const displayLabel = `${name}: ${percentage}%`;

  // Determine label color from the rest.color or fallback color arrays
  const sliceColor =
    rest.fill ||
    rest.color ||
    PIE_COLORS?.[index % PIE_COLORS.length] || '#000'; // fallback to black

  return (
    <text
      x={x}
      y={y}
      fill={sliceColor}
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      style={{
        fontSize: '12px',
        fontWeight: 500,
        maxWidth: '100px',
        overflow: 'hidden',
        whiteSpace: 'nowrap'
      }}
    >
      {displayLabel}
    </text>
  );
};


const renderCustomLabelSmall = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, ...rest }) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 1.8; // wider radius
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  const name = rest.name || rest.type;
  const percentage = rest.percentage || (percent * 100).toFixed(1);
  const displayLabel = `${name}: ${percentage}%`;

  const sliceColor = rest.fill || PIE_COLORS?.[index % PIE_COLORS.length] || '#000';

  return (
    <text
      x={x}
      y={y}
      fill={sliceColor}
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      style={{ fontSize: '12px', fontWeight: 500 }}
    >
      {displayLabel}
    </text>
  );
};

  // 7. Data for global CPF readability (Pie Chart)
  const getCPFReadability = () => {
    let readable = 0;
    let unreadable = 0;

    inspections.forEach(inspection => {
      // Filter for CPF site and ATEX certified equipment
      const isCPF = inspection.projet && inspection.projet.toLowerCase().includes('cpf');
      if (isCPF && isATEXCertified(inspection)) {
        if (isMarkingReadable(inspection)) {
          readable++;
        } else {
          unreadable++;
        }
      }
    });

    const total = readable + unreadable;
    return [
      {
        name: 'Lisibles',
        value: readable,
        percentage: total > 0 ? ((readable / total) * 100).toFixed(1) : 0
      },
      {
        name: 'Illisibles',
        value: unreadable,
        percentage: total > 0 ? ((unreadable / total) * 100).toFixed(1) : 0
      }
    ];
  };

  // 8. Data for unreadable ATEX equipment by type (Pie Chart)
  const getUnreadableATEXByType = () => {
    const typeData = {};

    inspections.forEach(inspection => {
      if (isATEXCertified(inspection) && !isMarkingReadable(inspection)) {
        const type = getEquipmentType(inspection);
        typeData[type] = (typeData[type] || 0) + 1;
      }
    });



  


    const total = Object.values(typeData).reduce((sum, count) => sum + count, 0);

    const result = Object.entries(typeData).map(([type, count]) => ({
      type,
      count,
      percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    }));

    console.log('Unreadable ATEX by type data:', result);
    return result;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  return (
  <Box
  sx={{
    backgroundColor: '#f5f5f5',
    minHeight: '100vh',
    marginLeft: '280px',     // Account for sidebar (external spacing)
    marginTop: '-650px',     // Move box upward (only if you really need it)
    marginRight: '0px',
    marginBottom: '0px',
    padding: '20px'          // Optional: internal spacing for content
  }}
>

      <Container maxWidth="xl" sx={{ pt: 0 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
          <Typography variant="h3" component="h1" sx={{ color: '#1976d2', mr: 2 }}>
            Tableau de Bord - Statistiques ATEX
          </Typography>
          <IconButton
            onClick={fetchInspections}
            disabled={loading}
            sx={{ color: '#1976d2' }}
            title="Actualiser les données"
          >
            <RefreshIcon />
          </IconButton>
      
        </Box>

        {inspections.length === 0 && !loading && !error && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              📊 Aucune inspection trouvée. Créez des inspections pour voir les statistiques ATEX.
            </Typography>
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body2">
              ⚠️ {error}
            </Typography>
          </Alert>
        )}



        

      {/* Summary Statistics */}
      <Grid container spacing={2} sx={{ mb: 4 }} justifyContent="center">
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e3f2fd' }}>
            <Typography variant="h4" color="primary">{inspections.length}</Typography>
            <Typography variant="body2">Total Inspections</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e8f5e8' }}>
            <Typography variant="h4" color="success.main">
              {inspections.filter(i => isATEXAdequate(i)).length}
            </Typography>
            <Typography variant="body2">Équipements Adéquats</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#fff3e0' }}>
            <Typography variant="h4" color="warning.main">
              {inspections.filter(i => isATEXCertified(i)).length}
            </Typography>
            <Typography variant="body2">Certifiés ATEX</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#fce4ec' }}>
            <Typography variant="h4" color="error.main">
              {inspections.filter(i => isATEXCertified(i) && !isMarkingReadable(i)).length}
            </Typography>
            <Typography variant="body2">Marquage Illisible</Typography>
          </Paper>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* 1. Bar Chart - Equipment adequacy by unit */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3, height: 450, display: 'flex', flexDirection: 'column' }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              1. Répartition de l'adéquation par unité
            </Typography>
            <Box sx={{ flexGrow: 1, minHeight: 350 }}>
              {getAdequacyByUnit().length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={getAdequacyByUnit()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="unite" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="nombre_adequats" fill={COLORS.adequate} name="Adéquats" />
                    <Bar dataKey="nombre_inadequats" fill={COLORS.inadequate} name="Inadéquats" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Typography color="text.secondary">Aucune donnée disponible</Typography>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Total: {getAdequacyByUnit().length} unités
            </Typography>
          </Paper>
        </Grid>

        {/* 2. Pie Chart - Global ATEX adequacy */}
       <Grid item xs={12} lg={10} md={8}>
          <Paper sx={{ p: 3, height: 450, display: 'flex', flexDirection: 'column' , width: '100%'}}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              2. Adéquation globale ATEX
            </Typography>
            <Box sx={{ flexGrow: 1, minHeight: 350 }}>
              {inspections.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={getGlobalAdequacy()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={renderCustomLabel}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {getGlobalAdequacy().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.adequate : COLORS.inadequate} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value} équipements`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Typography color="text.secondary">Aucune donnée disponible</Typography>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Total: {inspections.length} équipements analysés
            </Typography>
          </Paper>
        </Grid>

        {/* 3. Bar Chart - Inadequate equipment by type */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              3. Équipements inadéquats par type
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getInadequateByType()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type_equipement" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="nombre_adequats" fill={COLORS.adequate} name="Conformes" />
                <Bar dataKey="nombre_inadequats" fill={COLORS.inadequate} name="Non conformes" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Row containing only Chart 4 and 5 */}
<Grid item xs={12}>
  <Grid container spacing={3}>
    {/* Chart 4 - ATEX Certification (Full Width) */}
    <Grid item xs={12}>
      <Paper sx={{ p: 3, height: 400, display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
          4. Certification ATEX
        </Typography>
        <Box sx={{ flexGrow: 1, minHeight: 350 }}>
          {inspections.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={getATEXCertification()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {getATEXCertification().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.certified : COLORS.nonCertified} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography color="text.secondary">Aucune donnée disponible</Typography>
            </Box>
          )}
        </Box>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          Total: {inspections.length} équipements analysés
        </Typography>
      </Paper>
    </Grid>

    {/* Chart 5 - Non-ATEX Equipment by Type */}
    <Grid item xs={12} md={8}>
      <Paper sx={{ p: 3, height: 400 }}>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
          5. Équipements non ATEX par type
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={getNonATEXByType()}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {getNonATEXByType().map((entry, index) => (
                <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </Paper>
    </Grid>
  </Grid>
</Grid>


        {/* 6. Bar Chart - Marking readability by unit */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              6. Lisibilité marquage par unité
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getReadabilityByUnit()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="unite" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="lisibles" fill={COLORS.readable} name="Lisibles" />
                <Bar dataKey="illisibles" fill={COLORS.unreadable} name="Illisibles" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* 7. Pie Chart - Global CPF readability */}
        <Grid item xs={12} md={10}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              7. Lisibilité globale CPF
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={getCPFReadability()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {getCPFReadability().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.readable : COLORS.unreadable} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* 8. Pie Chart - Unreadable ATEX equipment by type */}
        <Grid item xs={12} lg={12} md={10}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              8. Marquage illisible par type
            </Typography>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={getUnreadableATEXByType()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {getUnreadableATEXByType().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    </Container>
    </Box>
  );
};

export default DashboardPage;