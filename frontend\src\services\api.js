import axios from 'axios';

// Determine the API base URL
const getApiBaseUrl = () => {
  // Check if we're in production (Render deployment)
  if (window.location.hostname.includes('onrender.com')) {
    // Use the backend domain since CORS is properly configured
    return 'https://e-inspection-form-3.onrender.com';
  }
  // Use environment variable or fallback to localhost
  return process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000';
};

const api = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  xsrfCookieName: 'csrftoken',
  xsrfHeaderName: 'X-CSRFToken',
});

// Add request interceptor to handle authentication
api.interceptors.request.use(
  (config) => {
    // Add authentication token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Token ${token}`;
    }

    // Get CSRF token from cookie (for session-based requests)
    const csrfToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('csrftoken='))
      ?.split('=')[1];

    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('currentUsername');
      // Redirect to login page
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response || error);
    return Promise.reject(error);
  }
);

export const login = async (username, password) => {
  try {
    const response = await api.post('/api/auth/login/', { username, password });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const logout = async () => {
  try {
    const response = await api.post('/api/auth/logout/');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const getCurrentUser = async (username = null) => {
  try {
    const url = username ? `/api/auth/profile/?username=${username}` : '/api/auth/profile/';
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const register = async (userData) => {
  try {
    const response = await api.post('/api/auth/register/', userData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const forgotPassword = async (email) => {
  try {
    const response = await api.post('/api/auth/forgot-password/', { email });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const resetPassword = async (token, newPassword) => {
  try {
    const response = await api.post('/api/auth/reset-password/', {
      token,
      new_password: newPassword
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const getInspections = async () => {
  try {
    const url = '/api/inspections/inspections/';
    console.log('Making API call to:', url);
    const response = await api.get(url);
    console.log('API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('API error in getInspections:', error);
    throw error.response?.data || error.message;
  }
};

export default api;