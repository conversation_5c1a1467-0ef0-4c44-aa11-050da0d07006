from django.db import models
from django.contrib.auth.models import User

class Inspection(models.Model):
    FICHE_GENERATION_CHOICES = [
        ('manual', 'Manual'),
        ('automatic', 'Automatic'),
    ]

    fiche_num = models.CharField(max_length=100)
    fiche_generation_method = models.CharField(max_length=20, choices=FICHE_GENERATION_CHOICES, default='manual')
    date = models.DateField(null=True, blank=True)
    projet = models.CharField(max_length=200, blank=True, null=True)
    equipement = models.CharField(max_length=200, blank=True, null=True)
    equipement_custom = models.CharField(max_length=200, blank=True, null=True)
    tag = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    constructeur = models.CharField(max_length=200, blank=True, null=True)
    model = models.CharField(max_length=200, blank=True, null=True)
    puissance = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    courant = models.CharField(max_length=100, blank=True, null=True)
    tension = models.CharField(max_length=100, blank=True, null=True)
    unite = models.CharField(max_length=100, blank=True, null=True)
    unite_custom = models.CharField(max_length=200, blank=True, null=True)
    localisation = models.CharField(max_length=200, blank=True, null=True)
    zone_atex = models.CharField(max_length=100, blank=True, null=True)
    groupe_gaz = models.CharField(max_length=100, blank=True, null=True)
    classe_t = models.CharField(max_length=100, blank=True, null=True)
    marquage_atex_g = models.CharField(max_length=100, blank=True, null=True)
    marquage_atex_d = models.CharField(max_length=100, blank=True, null=True)
    marquage_us = models.CharField(max_length=200, blank=True, null=True)
    type_marquage = models.CharField(max_length=100, blank=True, null=True)
    mode_protection = models.CharField(max_length=100, blank=True, null=True)
    organisme_notifie = models.CharField(max_length=100, blank=True, null=True)
    ip = models.CharField(max_length=100, blank=True, null=True)
    nema = models.CharField(max_length=100, blank=True, null=True)
    certificat = models.CharField(max_length=100, blank=True, null=True)
    tamb_min = models.CharField(max_length=100, blank=True, null=True)
    tamb_max = models.CharField(max_length=100, blank=True, null=True)
    atex_oui = models.BooleanField(default=False)
    atex_non = models.BooleanField(default=False)
    acces_inaccessible = models.BooleanField(default=False)
    acces_calorifuge = models.BooleanField(default=False)
    acces_peinte = models.BooleanField(default=False)
    acces_inaccessible_plaque = models.BooleanField(default=False)
    acces_illisible = models.BooleanField(default=False)
    acces_pas_plaque = models.BooleanField(default=False)
    niveau_1 = models.BooleanField(default=False)
    niveau_2 = models.BooleanField(default=False)
    niveau_3 = models.BooleanField(default=False)
    points = models.JSONField()
    observations = models.TextField(blank=True, null=True)
    action = models.TextField(blank=True, null=True)

    # Photo fields
    photo_marquage = models.ImageField(upload_to='inspection_photos/', null=True, blank=True)
    photo_equipement = models.ImageField(upload_to='inspection_photos/', null=True, blank=True)
    photos_anomalies = models.JSONField(default=list, blank=True)  # Store multiple photo paths

    pdf_file = models.FileField(upload_to='inspection_pdfs/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return f"Inspection {self.fiche_num} - {self.date}"