import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  InputAdornment,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import api, { getInspections } from '../services/api';

const Documents = () => {
  const [inspections, setInspections] = useState([]);
  const [filteredInspections, setFilteredInspections] = useState([]);
  const [selectedPdf, setSelectedPdf] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);

  const navigate = useNavigate();

  // Filter states
  const [filters, setFilters] = useState({
    date: '',
    project: '',
    niveau: '',
    equipement: ''
  });

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  // Available filter options
  const [filterOptions, setFilterOptions] = useState({
    projects: [],
    equipements: [],
    niveaux: ['N1', 'N2', 'N3']
  });

  useEffect(() => {
    fetchInspections();
  }, []);

  useEffect(() => {
    // Debounce search to avoid excessive filtering
    const timeoutId = setTimeout(() => {
      applyFilters();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [inspections, filters, searchQuery]);

  const fetchInspections = async () => {
    try {
      // Check if user is authenticated
      const authToken = localStorage.getItem('authToken');
      const currentUsername = localStorage.getItem('currentUsername');

      if (!authToken || !currentUsername) {
        console.error('Documents: No authentication token found');
        setInspections([]);
        return;
      }

      // Fetch user-specific inspections using token authentication
      console.log('Documents: Fetching inspections for authenticated user:', currentUsername);
      const inspectionsData = await getInspections();
      console.log('Documents: API response:', inspectionsData);
      console.log('Documents: Number of inspections:', inspectionsData?.length || 0);

      setInspections(inspectionsData || []);

      // Extract unique values for filter options
      const projects = [...new Set(inspectionsData.map(item => item.projet).filter(Boolean))];
      const equipements = [...new Set(inspectionsData.map(item => {
        return item.equipement === 'custom' && item.equipement_custom
          ? item.equipement_custom
          : item.equipement;
      }).filter(Boolean))];

      setFilterOptions({
        projects: projects.sort(),
        equipements: equipements.sort(),
        niveaux: ['N1', 'N2', 'N3']
      });
    } catch (error) {
      console.error('Error fetching inspections:', error);
    }
  };

  const applyFilters = () => {
    let filtered = [...inspections];

    // Search filter - apply first to search across all data
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(item => {
        // Search across multiple fields
        const searchableFields = [
          item.fiche_num || '',
          item.projet || '',
          item.equipement === 'custom' && item.equipement_custom
            ? item.equipement_custom
            : item.equipement || '',
          item.tag || '',
          item.localisation || '',
          item.date || '',
          // Include niveau in search
          formatNiveaux(item)
        ];

        return searchableFields.some(field =>
          field.toString().toLowerCase().includes(query)
        );
      });
    }

    // Date filter
    if (filters.date) {
      filtered = filtered.filter(item => item.date === filters.date);
    }

    // Project filter
    if (filters.project) {
      filtered = filtered.filter(item => item.projet === filters.project);
    }

    // Equipment filter
    if (filters.equipement) {
      filtered = filtered.filter(item => {
        const equipement = item.equipement === 'custom' && item.equipement_custom
          ? item.equipement_custom
          : item.equipement;
        return equipement === filters.equipement;
      });
    }

    // Niveau filter
    if (filters.niveau) {
      filtered = filtered.filter(item => {
        switch (filters.niveau) {
          case 'N1': return item.niveau_1;
          case 'N2': return item.niveau_2;
          case 'N3': return item.niveau_3;
          default: return true;
        }
      });
    }

    setFilteredInspections(filtered);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      date: '',
      project: '',
      niveau: '',
      equipement: ''
    });
    setSearchQuery('');
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const getActiveFiltersCount = () => {
    const filterCount = Object.values(filters).filter(value => value !== '').length;
    const searchCount = searchQuery.trim() ? 1 : 0;
    return filterCount + searchCount;
  };

  const handleSearchChange = (value) => {
    setSearchQuery(value);
  };

  const handleViewPdf = async (inspectionId) => {
    try {
      console.log('Attempting to view PDF for inspection ID:', inspectionId);
      const response = await api.get(`/api/inspections/inspections/${inspectionId}/generate_pdf/`, {
        responseType: 'blob'
      });
      console.log('PDF response received:', response);
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      setSelectedPdf(url);
      setOpenDialog(true);
    } catch (error) {
      console.error('Error fetching PDF:', error);
      alert(`Error generating PDF: ${error.message || 'Unknown error'}`);
    }
  };

  const handleDownloadPdf = async (inspectionId, ficheNum, date) => {
    try {
      console.log('Attempting to download PDF for inspection ID:', inspectionId);
      const response = await api.get(`/api/inspections/inspections/${inspectionId}/generate_pdf/`, {
        responseType: 'blob'
      });
      console.log('PDF download response received:', response);
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `inspection_${ficheNum}_${date}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert(`Error downloading PDF: ${error.message || 'Unknown error'}`);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    if (selectedPdf) {
      window.URL.revokeObjectURL(selectedPdf);
      setSelectedPdf(null);
    }
  };

  const handleEditInspection = (inspection) => {
    // Navigate to inspection form with the inspection data for editing
    // This will create a new inspection based on the existing one
    navigate('/InspectionFormPage', {
      state: {
        editData: inspection,
        isEdit: true
      }
    });
  };

  const formatNiveaux = (inspection) => {
    const niveaux = [];
    if (inspection.niveau_1) niveaux.push('N1');
    if (inspection.niveau_2) niveaux.push('N2');
    if (inspection.niveau_3) niveaux.push('N3');
    return niveaux.length > 0 ? niveaux.join(', ') : '-';
  };



  return (
    <Box sx={{
      p: 0,
      marginLeft: '280px',
      marginTop: '-600px',
      marginRight: '0px'
    }}>
     

      {/* Filters Section */}
      <Card sx={{ marginLeft: '20px', marginRight: '20px', marginBottom: '20px' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
            <FilterIcon sx={{ marginRight: 1 }} />
            <Typography variant="h6">
              Filtres
              {getActiveFiltersCount() > 0 && (
                <Chip
                  label={`${getActiveFiltersCount()} active`}
                  size="small"
                  color="primary"
                  sx={{ marginLeft: 1 }}
                />
              )}
            </Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Button
              startIcon={<ClearIcon />}
              onClick={clearFilters}
              disabled={getActiveFiltersCount() === 0}
              size="small"
            >
              Clear All
            </Button>

          </Box>

          <Divider sx={{ marginBottom: 2 }} />

          {/* Search Section */}
          <Box sx={{ marginBottom: 3 }}>
            <TextField
              label="Rechercher des inspections..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              fullWidth
              size="medium"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchQuery && (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={clearSearch}
                      edge="end"
                      size="small"
                      title="Clear search"
                    >
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                )
              }}
              placeholder="Rechercher par numéro de fiche, projet, équipement, tag, emplacement, date ou niveau..."
            />
          </Box>

          <Grid container spacing={3}>
            {/* Date Filter */}
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="Date"
                type="date"
                value={filters.date}
                onChange={(e) => handleFilterChange('date', e.target.value)}
                slotProps={{
                  inputLabel: { shrink: true }
                }}
                fullWidth
                size="medium"
              />
            </Grid>

            {/* Project Filter */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth size="medium">
                <InputLabel>Projet</InputLabel>
                <Select
                  value={filters.project}
                  onChange={(e) => handleFilterChange('project', e.target.value)}
                  label="Project"
                  sx={{ minWidth: 200 }}
                >
                  <MenuItem value="">Tous les Projets</MenuItem>
                  {filterOptions.projects.map((project) => (
                    <MenuItem key={project} value={project}>
                      {project}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Equipment Filter */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="medium">
                <InputLabel>Équipement</InputLabel>
                <Select
                  value={filters.equipement}
                  onChange={(e) => handleFilterChange('equipement', e.target.value)}
                  label="Equipment"
                  sx={{ minWidth: 180 }}
                >
                  <MenuItem value="">Tous les Équipement</MenuItem>
                  {filterOptions.equipements.map((equipement) => (
                    <MenuItem key={equipement} value={equipement}>
                      {equipement}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Niveau Filter */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="medium">
                <InputLabel>Niveau</InputLabel>
                <Select
                  value={filters.niveau}
                  onChange={(e) => handleFilterChange('niveau', e.target.value)}
                  label="Niveau"
                  sx={{ minWidth: 150 }}
                >
                  <MenuItem value="">Tou les Niveaux</MenuItem>
                  {filterOptions.niveaux.map((niveau) => (
                    <MenuItem key={niveau} value={niveau}>
                      {niveau}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {/* Results Summary */}
      <Box sx={{ marginLeft: '20px', marginBottom: '10px' }}>
        <Typography variant="body2" color="text.secondary">
          Showing {filteredInspections.length} of {inspections.length} inspections
          {searchQuery.trim() && ' (searched)'}
          {Object.values(filters).some(value => value !== '') && ' (filtered)'}
          {searchQuery.trim() && Object.values(filters).some(value => value !== '') && ' (searched & filtered)'}
        </Typography>
      </Box>

      <TableContainer
        component={Paper}
        sx={{
          marginLeft: '20px',
          marginTop: '10px'
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Numéro de Fiche </TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Projet</TableCell>
              <TableCell>Équipement</TableCell>
              <TableCell>Niveau</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredInspections.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    {inspections.length === 0
                      ? 'No inspections found.'
                      : searchQuery.trim() && Object.values(filters).some(value => value !== '')
                      ? 'No inspections match the current search and filters.'
                      : searchQuery.trim()
                      ? 'No inspections match the current search.'
                      : 'No inspections match the current filters.'}
                  </Typography>
                  {getActiveFiltersCount() > 0 && inspections.length > 0 && (
                    <Button
                      onClick={clearFilters}
                      size="small"
                      sx={{ mt: 1 }}
                    >
                      Clear All Filters & Search
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              filteredInspections.map((inspection) => (
                <TableRow key={inspection.id}>
                  <TableCell>{inspection.fiche_num}</TableCell>
                  <TableCell>{inspection.date}</TableCell>
                  <TableCell>{inspection.projet}</TableCell>
                  <TableCell>
                    {inspection.equipement === 'custom' && inspection.equipement_custom
                      ? inspection.equipement_custom
                      : inspection.equipement}
                  </TableCell>
                  <TableCell>
                    <Box sx={{
                      display: 'flex',
                      gap: 0.5,
                      flexWrap: 'wrap'
                    }}>
                      {formatNiveaux(inspection)}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => handleViewPdf(inspection.id)}
                      color="primary"
                      title="View PDF"
                      size="small"
                    >
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleDownloadPdf(inspection.id, inspection.fiche_num, inspection.date)}
                      color="primary"
                      title="Download PDF"
                      size="small"
                    >
                      <DownloadIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleEditInspection(inspection)}
                      color="primary"
                      title="Edit (Create New)"
                      size="small"
                    >
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Inspection PDF</DialogTitle>
        <DialogContent>
          {selectedPdf && (
            <iframe
              src={selectedPdf}
              style={{ width: '100%', height: '80vh' }}
              title="PDF Viewer"
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>


    </Box>
  );
};

export default Documents;