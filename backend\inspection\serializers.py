from rest_framework import serializers
from .models import Inspection
import json

class J<PERSON><PERSON>ield(serializers.Field):
    """Custom field to handle JSON data that might come as strings in FormData"""

    def __init__(self, default_value=None, **kwargs):
        self.default_value = default_value
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        print(f"DEBUG: JSONField.to_internal_value called with data: {type(data)} - {repr(data)[:100]}")

        if data is None or data == '' or data == 'null':
            return self.default_value if self.default_value is not None else {}

        if isinstance(data, str):
            try:
                parsed = json.loads(data)
                print(f"DEBUG: JSONField.to_internal_value - Successfully parsed JSON: {type(parsed)}")
                return parsed
            except (json.JSONDecodeError, TypeError) as e:
                print(f"DEBUG: JSONField.to_internal_value - JSON parse error: {e}")
                raise serializers.ValidationError(f"Invalid JSON format: {e}")
        elif isinstance(data, (dict, list)):
            print(f"DEBUG: JSONField.to_internal_value - Data is already parsed: {type(data)}")
            return data
        else:
            print(f"DEBUG: JSONField.to_internal_value - Unexpected data type: {type(data)}")
            raise serializers.ValidationError("Expected JSON object or array")

    def to_representation(self, value):
        return value

class InspectionSerializer(serializers.ModelSerializer):
    equipement_custom = serializers.CharField(required=False, allow_blank=True)
    unite_custom = serializers.CharField(required=False, allow_blank=True)
    marquage_us = serializers.CharField(required=False, allow_blank=True)
    points = JSONField(default_value={})
    photos_anomalies = JSONField(default_value=[])

    # Photo fields properly defined as ImageField with proper handling
    photo_marquage = serializers.ImageField(required=False, allow_null=True)
    photo_equipement = serializers.ImageField(required=False, allow_null=True)

    class Meta:
        model = Inspection
        fields = [
            'id', 'fiche_num', 'fiche_generation_method', 'date', 'projet', 'equipement', 'equipement_custom', 'tag',
            'constructeur', 'model', 'puissance', 'courant', 'tension',
            'unite', 'unite_custom', 'localisation', 'zone_atex', 'groupe_gaz', 'classe_t',
            'marquage_atex_g', 'marquage_atex_d', 'marquage_us', 'type_marquage',
            'mode_protection', 'organisme_notifie', 'ip', 'nema',
            'certificat', 'tamb_min', 'tamb_max', 'atex_oui', 'atex_non',
            'acces_inaccessible', 'acces_calorifuge', 'acces_peinte',
            'acces_inaccessible_plaque', 'acces_illisible',
            'acces_pas_plaque', 'niveau_1', 'niveau_2', 'niveau_3',
            'points', 'observations', 'action',
            'photo_marquage', 'photo_equipement', 'photos_anomalies',
            'created_at', 'created_by', 'pdf_file'
        ]
        read_only_fields = ('created_at', 'created_by', 'pdf_file')